<?php

namespace App\Services;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesService
{

    private array $selections;
    private array $groupBy;
    /**
     * @var true
     */
    private bool $isNegativeCeiling = false;


    public function addSelect($select): static
    {
        Log::debug('SalesService: Adding custom selection', [
            'selection' => $select,
            'current_selections_count' => count($this->selections)
        ]);

        $this->selections[] = $select;

        return $this;
    }

    public function forNegativeCeiling()
    {
        Log::debug('SalesService: Enabling negative ceiling mode', [
            'previous_state' => $this->isNegativeCeiling
        ]);

        $this->isNegativeCeiling = true;
        return $this;
    }

    private function __construct(
        private readonly SaleDistribution $saleDistribution,
        private readonly ?DistributionType $distributionType = null
    )
    {
        $this->selections = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
            DB::raw('CAST(SUM(crm_sales_details.quantity) AS DECIMAL(64,20)) /
                   SUM(SUM(crm_sales_details.quantity)) OVER (PARTITION BY crm_sales.product_id) AS percentage')
        ];

        $this->groupBy = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
        ];

        // Temporarily disabled logging to avoid permission issues
        // Log::debug('SalesService: Initialized', [
        //     'sale_distribution' => $saleDistribution->name,
        //     'distribution_type' => $distributionType?->value,
        //     'selections_count' => count($this->selections),
        //     'group_by_count' => count($this->groupBy)
        // ]);
    }

    public static function make(SaleDistribution $saleDistribution, ?DistributionType $distributionType = null): static
    {
        // Temporarily disabled logging
        // Log::debug('SalesService: Creating new instance', [
        //     'sale_distribution' => $saleDistribution->name,
        //     'distribution_type' => $distributionType?->value
        // ]);

        return new static($saleDistribution, $distributionType);
    }

    private function generateCacheKey(string $functionName, array $parameters): string
    {
        // Include all instance state in cache key to prevent conflicts in Octane environments
        $cacheKeyData = [
            'function' => $functionName,
            'parameters' => $parameters,
            'distribution_type' => $this->distributionType?->value,
            'sale_distribution' => $this->saleDistribution->name,
            'is_negative_ceiling' => $this->isNegativeCeiling,
            'selections' => $this->selections,
            'group_by' => $this->groupBy,
            // Add instance identifier to ensure cache isolation between different service instances
            'instance_hash' => spl_object_id($this)
        ];

        $paramsString = serialize($cacheKeyData);
        $key = $functionName . '|' . $paramsString;
        $hashedKey = md5($key);

        Log::debug('SalesService: Generated cache key with enhanced isolation', [
            'function_name' => $functionName,
            'parameters_count' => count($parameters),
            'distribution_type' => $this->distributionType?->value,
            'sale_distribution' => $this->saleDistribution->name,
            'is_negative_ceiling' => $this->isNegativeCeiling,
            'selections_count' => count($this->selections),
            'group_by_count' => count($this->groupBy),
            'instance_id' => spl_object_id($this),
            'serialized_length' => strlen($paramsString),
            'cache_key_hash' => $hashedKey
        ]);

        return $hashedKey;
    }

    public function getRatiosForDistribution(string $date, int $product, array $distributorIds, array $divisionIds = []): Collection
    {
        // Validate input parameters
        if (empty($distributorIds)) {
            Log::warning('SalesService: Empty distributor IDs provided', [
                'date' => $date,
                'product_id' => $product,
                'context' => 'PARAMETER_VALIDATION'
            ]);
            return collect();
        }

        $date = Carbon::parse($date);

        Log::info('SalesService: Getting ratios for distribution', [
            'date' => $date->format('Y-m-d'),
            'product_id' => $product,
            'distributor_ids' => $distributorIds,
            'distributor_ids_count' => count($distributorIds),
            'division_ids' => $divisionIds,
            'division_ids_count' => count($divisionIds),
            'sale_distribution' => $this->saleDistribution->name,
            'distribution_type' => $this->distributionType?->value,
            'is_negative_ceiling' => $this->isNegativeCeiling,
            'context' => 'STORES_DISTRIBUTION_DEBUG',
            'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
        ]);

        DB::statement("set sql_mode=''");

        $cacheKey = $this->generateCacheKey(__FUNCTION__, [$this->saleDistribution, $this->isNegativeCeiling, ...$this->selections, ...func_get_args()]);

        Log::debug('SalesService: Cache key generated', [
            'cache_key' => $cacheKey,
            'cache_ttl_hours' => 2
        ]);

        // Reduce cache TTL for distribution queries to prevent stale data issues
        $cacheTTL = now()->addMinutes(30); // Reduced from 2 hours to 30 minutes

        return Cache::remember(
            $cacheKey,
            $cacheTTL,
            function () use ($date, $distributorIds, $product, $divisionIds) {
                Log::debug('SalesService: Cache miss, executing database query', [
                    'date' => $date->format('Y-m-d'),
                    'product_id' => $product,
                    'distriubtor_ids' => $distributorIds,
                    'division_ids' => $divisionIds
                ]);

                $query = Sale::query();
                $query->select($this->selections);
                $this->buildJoins($query,$date, $product, $divisionIds);
                $ceilings = $this->getCeiling();

                Log::debug('SalesService: Query conditions applied', [
                    'year' => $date->year,
                    'month' => $date->month,
                    'distributor_ids_count' => count($distributorIds),
                    'ceilings' => array_map(fn($c) => $c->value, $ceilings),
                    'group_by_fields' => $this->groupBy
                ]);

                $query
                    ->whereYear("sales.date", (string)$date->year)
                    ->whereMonth("sales.date", (string)$date->month)
                    ->whereIn("sales.distributor_id", $distributorIds)
                    ->whereIn("sales.ceiling", $ceilings);
                $query->groupBy($this->groupBy);

                // Log the actual SQL query being executed
                $sql = $query->toSql();
                $bindings = $query->getBindings();

                Log::debug('SalesService: Executing SQL query', [
                    'sql' => $sql,
                    'bindings' => $bindings,
                    'date' => $date->format('Y-m-d'),
                    'product_id' => $product,
                    'distributor_ids' => $distributorIds,
                    'distribution_type' => $this->distributionType?->value
                ]);

                $results = $query->get();

                // Log detailed results for debugging
                Log::info('SalesService: Distribution ratios retrieved', [
                    'date' => $date->format('Y-m-d'),
                    'product_id' => $product,
                    'ratios_count' => $results->count(),
                    'total_percentage' => $results->sum('percentage'),
                    'distribution_type' => $this->distributionType?->value ?? 'default',
                    'sale_distribution' => $this->saleDistribution->name,
                    'sample_results' => $results->take(3)->toArray() // Show first 3 results for debugging
                ]);

                // If no results, log additional debugging info
                if ($results->count() === 0) {
                    Log::warning('SalesService: No distribution ratios found - investigating', [
                        'date' => $date->format('Y-m-d'),
                        'product_id' => $product,
                        'distributor_ids' => $distributorIds,
                        'division_ids' => $divisionIds,
                        'distribution_type' => $this->distributionType?->value,
                        'ceilings' => array_map(fn($c) => $c->value, $ceilings),
                        'query_conditions' => [
                            'year' => $date->year,
                            'month' => $date->month,
                            'has_distribution_type' => $this->distributionType !== null,
                            'has_division_filter' => !empty($divisionIds)
                        ]
                    ]);
                }

                return $results;
            }
        );
    }

    private function buildJoins($query,Carbon $date, int $product, array $divisionIds): void
    {
        Log::debug('SalesService: Building query joins', [
            'date' => $date->format('Y-m-d'),
            'product_id' => $product,
            'division_ids_count' => count($divisionIds),
            'has_division_filter' => !empty($divisionIds),
            'distribution_type' => $this->distributionType?->value
        ]);

        $query
            ->join("sales_details", "sales.id", "sales_details.sale_id")
            ->join("line_products", function ($join) use ($date, $product) {
                $join
                    ->on("sales.product_id", "=", "line_products.product_id")
                    ->where("line_products.product_id", $product)
                    ->whereColumn("line_products.line_id", "sales_details.line_id")
                    ->where("line_products.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_products.to_date", ">", $date)
                            ->orWhereNull("line_products.to_date")
                    );
            })
            ->join("line_divisions", function ($join) use ($date, $divisionIds) {
                $join
                    ->on("sales_details.div_id", "=", "line_divisions.id")
                    ->whereColumn("line_divisions.line_id", "line_products.line_id")
                    ->where("line_divisions.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_divisions.to_date", ">", $date)
                            ->orWhereNull("line_divisions.to_date")
                    );

                if (!empty($divisionIds)) {
                    $join->whereIn('line_divisions.id', $divisionIds);
                    Log::debug('SalesService: Applied division ID filter', [
                        'division_ids' => $divisionIds
                    ]);
                }
            });

        // Add mappings joins when DistributionType is specified
        if ($this->distributionType !== null) {
            Log::debug('SalesService: Adding mappings joins for distribution type', [
                'distribution_type' => $this->distributionType->value,
                'is_private_pharmacy' => $this->distributionType === DistributionType::PRIVATE_PHARMACY
            ]);

            $query
                ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
                ->join("mappings", function ($join) {
                    $join->on('mapping_sale.mapping_id', 'mappings.id');

                    if ($this->distributionType === DistributionType::PRIVATE_PHARMACY) {
                        Log::debug('SalesService: Applying PRIVATE_PHARMACY mapping filter (excluding STORES and LOCAL_CHAINS)');
                        $join->where(function ($q) {
                            $q->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::STORES->value)
                                ->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::LOCAL_CHAINS->value)
                                ->orWhere('mappings.unified_pharmacy_type_id', null);
                        });
                    } else {
                        Log::debug('SalesService: Applying specific distribution type mapping filter', [
                            'distribution_type_value' => $this->distributionType->value
                        ]);
                        $join->where('mappings.unified_pharmacy_type_id', $this->distributionType->value);
                    }
                })
                ->where("mappings.exception", false);
        } else {
            Log::debug('SalesService: No distribution type specified, skipping mappings joins');
        }

        if ($this->saleDistribution == SaleDistribution::NORMAL) {
            // Use LEFT JOIN for STORES distribution type, INNER JOIN for others (matching DistributionService logic)
            $joinType = ($this->distributionType === DistributionType::STORES) ? 'leftJoin' : 'join';

            Log::debug('SalesService: Adding product ceilings join', [
                'sale_distribution' => $this->saleDistribution->name,
                'join_type' => $joinType,
                'distribution_type' => $this->distributionType?->value,
                'is_stores_type' => $this->distributionType === DistributionType::STORES,
                'will_apply_quantity_constraints' => $this->distributionType !== DistributionType::STORES
            ]);

            $query->$joinType("product_ceilings", function ($join) use ($date) {
                $join
                    ->on("product_ceilings.product_id", "=", "sales.product_id")
                    ->where("product_ceilings.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("product_ceilings.to_date", ">", $date)
                            ->orWhereNull("product_ceilings.to_date")
                    );

                // Only add quantity constraints for INNER JOIN (non-STORES types)
                if ($this->distributionType !== DistributionType::STORES) {
                    Log::debug('SalesService: Applying quantity constraints for non-STORES distribution type');
                    $join->where(function ($q) {
                        $q->whereColumn('sales.quantity', '>=', 'product_ceilings.negative_units')
                            ->whereColumn('sales.quantity', '<=', 'product_ceilings.units');
                    });
                } else {
                    Log::debug('SalesService: Skipping quantity constraints for STORES distribution type (LEFT JOIN)');
                }
            });
        } else {
            Log::debug('SalesService: Skipping product ceilings join for non-NORMAL sale distribution', [
                'sale_distribution' => $this->saleDistribution->name
            ]);
        }

        Log::debug('SalesService: Query joins completed');
    }

    private function getCeiling(): array
    {
        $ceilings = match ($this->saleDistribution) {
            SaleDistribution::NORMAL => [Ceiling::BELOW],
            SaleDistribution::DIRECT => [Ceiling::BELOW, Ceiling::DISTRIBUTED]
        };

        Log::debug('SalesService: Ceiling types determined', [
            'sale_distribution' => $this->saleDistribution->name,
            'ceilings' => array_map(fn($c) => $c->value, $ceilings),
            'is_negative_ceiling' => $this->isNegativeCeiling
        ]);

        return $ceilings;
    }
}
